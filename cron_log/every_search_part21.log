Using Spark's default log4j profile: org/apache/spark/log4j-defaults.properties
25/08/03 11:30:07 INFO SQLChecker: check sql success: 
select query, sum(expo) total_expo, nvl(sum(click), 0) total_click from
wbsearch.olap_video_mid_query_ctr_hour where unix_timestamp(concat(dt, lpad(hour, 2, '0')), 'yyyyMMddHH') >= 1754105401
group by query

25/08/03 11:30:07 INFO SQLRunner: all sql are correct!!!
25/08/03 11:30:07 INFO SQLRunner: localAppFiles=/tmp/spark_sp_tf_90b407e3543c4fe18696e1996c56c073
25/08/03 11:30:07 INFO SQLRunner: tempSQLFile=spark_sp_tf_90b407e3543c4fe18696e1996c56c073
Spark Command: /usr/local/jdk1.8.0_131/bin/java -cp /data0/spark/spark-3.2.0-bin/conf/:/data0/spark/spark-3.2.0-bin/jars/*:/usr/local/hadoop-2.7.3/etc/hadoop/ org.apache.spark.deploy.SparkSubmit --master yarn --deploy-mode cluster --class com.sina.is.sparksql.Main --name SparkSQL_Package --files /tmp/spark_sp_tf_90b407e3543c4fe18696e1996c56c073 /data1/spark/spark-3.2.0-bin/tools/sparksql-package-1.0.jar --file spark_sp_tf_90b407e3543c4fe18696e1996c56c073
========================================
25/08/03 11:30:11 INFO Client: Requesting a new application from cluster with 6122 NodeManagers
25/08/03 11:30:14 INFO Client: Verifying our application has not requested more than the maximum memory capability of the cluster (32768 MB per container)
25/08/03 11:30:14 INFO Client: Will allocate AM container, with 4096 MB memory including 1024 MB overhead
25/08/03 11:30:14 INFO Client: Setting up container launch context for our AM
25/08/03 11:30:14 INFO Client: Setting up the launch environment for our AM container
25/08/03 11:30:14 INFO Client: Preparing resources for our AM container
25/08/03 11:30:14 INFO Client: Source and destination file systems are the same. Not copying viewfs://c9/spark/jars/spark3.2/spark-3.2.0_20240408.zip
25/08/03 11:30:15 INFO Client: Uploading resource file:/data1/spark/spark-3.2.0-bin/tools/sparksql-package-1.0.jar -> viewfs://c9/wbsearch/weibo_search_quality/.sparkStaging/application_1742282717592_40426571/sparksql-package-1.0.jar
25/08/03 11:30:16 INFO Client: Uploading resource file:/tmp/spark_sp_tf_90b407e3543c4fe18696e1996c56c073 -> viewfs://c9/wbsearch/weibo_search_quality/.sparkStaging/application_1742282717592_40426571/spark_sp_tf_90b407e3543c4fe18696e1996c56c073
25/08/03 11:30:17 INFO Client: Uploading resource file:/tmp/spark-76fdc2b2-85ee-4acd-8eaf-100d1282bef8/__spark_conf__4904337147156777546.zip -> viewfs://c9/wbsearch/weibo_search_quality/.sparkStaging/application_1742282717592_40426571/__spark_conf__.zip
25/08/03 11:30:17 INFO SecurityManager: Changing view acls to: weibo_search_quality
25/08/03 11:30:17 INFO SecurityManager: Changing modify acls to: weibo_search_quality
25/08/03 11:30:17 INFO SecurityManager: Changing view acls groups to: 
25/08/03 11:30:17 INFO SecurityManager: Changing modify acls groups to: 
25/08/03 11:30:17 INFO SecurityManager: SecurityManager: authentication disabled; ui acls disabled; users  with view permissions: Set(weibo_search_quality); groups with view permissions: Set(); users  with modify permissions: Set(weibo_search_quality); groups with modify permissions: Set()
25/08/03 11:30:17 INFO Client: Submitting application application_1742282717592_40426571 to ResourceManager
25/08/03 11:30:17 INFO YarnClientImpl: Submitted application application_1742282717592_40426571
25/08/03 11:30:17 INFO Client: add a clear select app path hook
25/08/03 11:30:20 INFO Client: Application report for application_1742282717592_40426571 (state: ACCEPTED)
25/08/03 11:30:20 INFO Client: 
	 client token: N/A
	 diagnostics: N/A
	 ApplicationMaster host: N/A
	 ApplicationMaster RPC port: -1
	 queue: root.weibo_search.weibo_search_quality
	 start time: 1754191817327
	 final status: UNDEFINED
	 tracking URL: http://rm1.offline.hadoop.data.sina.com.cn:9008/proxy/application_1742282717592_40426571/
	 user: weibo_search_quality
25/08/03 11:30:23 INFO Client: Application report for application_1742282717592_40426571 (state: ACCEPTED)
25/08/03 11:30:26 INFO Client: Application report for application_1742282717592_40426571 (state: ACCEPTED)
25/08/03 11:30:29 INFO Client: Application report for application_1742282717592_40426571 (state: ACCEPTED)
25/08/03 11:30:32 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:30:32 INFO Client: 
	 client token: N/A
	 diagnostics: N/A
	 ApplicationMaster host: hj3026085025.hadoop.data.sina.com.cn
	 ApplicationMaster RPC port: 42245
	 queue: root.weibo_search.weibo_search_quality
	 start time: 1754191817327
	 final status: UNDEFINED
	 tracking URL: http://rm1.offline.hadoop.data.sina.com.cn:9008/proxy/application_1742282717592_40426571/
	 user: weibo_search_quality
25/08/03 11:30:35 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:30:38 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:30:41 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:30:44 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:30:47 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:30:50 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:30:53 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:30:56 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:30:59 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:31:02 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:31:05 INFO Client: Application report for application_1742282717592_40426571 (state: RUNNING)
25/08/03 11:31:08 INFO Client: Application report for application_1742282717592_40426571 (state: FINISHED)
25/08/03 11:31:08 INFO Client: 
	 client token: N/A
	 diagnostics: N/A
	 ApplicationMaster host: hj3026085025.hadoop.data.sina.com.cn
	 ApplicationMaster RPC port: 42245
	 queue: root.weibo_search.weibo_search_quality
	 start time: 1754191817327
	 final status: SUCCEEDED
	 tracking URL: http://rm1.offline.hadoop.data.sina.com.cn:9008/proxy/application_1742282717592_40426571/
	 user: weibo_search_quality
25/08/03 11:31:08 INFO Client: try to read data from path viewfs://c9/wbsearch/weibo_search_quality/.selectStaging/application_1742282717592_40426571/select_data, files are: part-00000-3bab511d-2897-465e-83a6-dddbb0cbe5b7-c000.txt,part-00001-3bab511d-2897-465e-83a6-dddbb0cbe5b7-c000.txt,part-00002-3bab511d-2897-465e-83a6-dddbb0cbe5b7-c000.txt
25/08/03 11:31:08 INFO Client: ---------------query finished, result set---------------
25/08/03 11:31:09 INFO Client: ---------------783476 rows selected (51 seconds)---------------
