/* Generated by Parser/pgen */

#define single_input 256
#define file_input 257
#define eval_input 258
#define decorator 259
#define decorators 260
#define decorated 261
#define async_funcdef 262
#define funcdef 263
#define parameters 264
#define typedargslist 265
#define tfpdef 266
#define varargslist 267
#define vfpdef 268
#define stmt 269
#define simple_stmt 270
#define small_stmt 271
#define expr_stmt 272
#define annassign 273
#define testlist_star_expr 274
#define augassign 275
#define del_stmt 276
#define pass_stmt 277
#define flow_stmt 278
#define break_stmt 279
#define continue_stmt 280
#define return_stmt 281
#define yield_stmt 282
#define raise_stmt 283
#define import_stmt 284
#define import_name 285
#define import_from 286
#define import_as_name 287
#define dotted_as_name 288
#define import_as_names 289
#define dotted_as_names 290
#define dotted_name 291
#define global_stmt 292
#define nonlocal_stmt 293
#define assert_stmt 294
#define compound_stmt 295
#define async_stmt 296
#define if_stmt 297
#define while_stmt 298
#define for_stmt 299
#define try_stmt 300
#define with_stmt 301
#define with_item 302
#define except_clause 303
#define suite 304
#define test 305
#define test_nocond 306
#define lambdef 307
#define lambdef_nocond 308
#define or_test 309
#define and_test 310
#define not_test 311
#define comparison 312
#define comp_op 313
#define star_expr 314
#define expr 315
#define xor_expr 316
#define and_expr 317
#define shift_expr 318
#define arith_expr 319
#define term 320
#define factor 321
#define power 322
#define atom_expr 323
#define atom 324
#define testlist_comp 325
#define trailer 326
#define subscriptlist 327
#define subscript 328
#define sliceop 329
#define exprlist 330
#define testlist 331
#define dictorsetmaker 332
#define classdef 333
#define arglist 334
#define argument 335
#define comp_iter 336
#define comp_for 337
#define comp_if 338
#define encoding_decl 339
#define yield_expr 340
#define yield_arg 341
