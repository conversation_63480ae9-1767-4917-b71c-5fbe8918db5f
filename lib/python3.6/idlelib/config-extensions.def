# config-extensions.def
#
# IDLE reads several config files to determine user preferences.  This
# file is the default configuration file for IDLE extensions settings.
#
# Each extension must have at least one section, named after the
# extension module. This section must contain an 'enable' item (=True to
# enable the extension, =False to disable it), it may contain
# 'enable_editor' or 'enable_shell' items, to apply it only to editor ir
# shell windows, and may also contain any other general configuration
# items for the extension.  Other True/False values will also be
# recognized as boolean by the Extension Configuration dialog.
#
# Each extension must define at least one section named
# ExtensionName_bindings or ExtensionName_cfgBindings. If present,
# ExtensionName_bindings defines virtual event bindings for the
# extension that are not user re-configurable. If present,
# ExtensionName_cfgBindings defines virtual event bindings for the
# extension that may be sensibly re-configured.
#
# If there are no keybindings for a menus' virtual events, include lines
# like <<toggle-code-context>>=  (See [CodeContext], below.)
#
# Currently it is necessary to manually modify this file to change
# extension key bindings and default values. To customize, create
# ~/.idlerc/config-extensions.cfg and append the appropriate customized
# section(s).  Those sections will override the defaults in this file.
#
# Note: If a keybinding is already in use when the extension is loaded,
# the extension's virtual event's keybinding will be set to ''.
#
# See config-keys.def for notes on specifying keys and extend.txt for
# information on creating IDLE extensions.

[AutoComplete]
enable=True
popupwait=2000
[AutoComplete_cfgBindings]
force-open-completions=<Control-Key-space>
[AutoComplete_bindings]
autocomplete=<Key-Tab>
try-open-completions=<KeyRelease-period> <KeyRelease-slash> <KeyRelease-backslash>

[AutoExpand]
enable=True
[AutoExpand_cfgBindings]
expand-word=<Alt-Key-slash>

[CallTips]
enable=True
[CallTips_cfgBindings]
force-open-calltip=<Control-Key-backslash>
[CallTips_bindings]
try-open-calltip=<KeyRelease-parenleft>
refresh-calltip=<KeyRelease-parenright> <KeyRelease-0>

[CodeContext]
enable=True
enable_shell=False
numlines=3
visible=False
bgcolor=LightGray
fgcolor=Black
[CodeContext_bindings]
toggle-code-context=

[FormatParagraph]
enable=True
max-width=72
[FormatParagraph_cfgBindings]
format-paragraph=<Alt-Key-q>

[ParenMatch]
enable=True
style= expression
flash-delay= 500
bell=True
[ParenMatch_cfgBindings]
flash-paren=<Control-Key-0>
[ParenMatch_bindings]
paren-closed=<KeyRelease-parenright> <KeyRelease-bracketright> <KeyRelease-braceright>

[RstripExtension]
enable=True
enable_shell=False
enable_editor=True

[ScriptBinding]
enable=True
enable_shell=False
enable_editor=True
[ScriptBinding_cfgBindings]
run-module=<Key-F5>
check-module=<Alt-Key-x>

[ZoomHeight]
enable=True
[ZoomHeight_cfgBindings]
zoom-height=<Alt-Key-2>
