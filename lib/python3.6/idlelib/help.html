<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <title>25.5. IDLE &mdash; Python 3.5.2 documentation</title>

    <link rel="stylesheet" href="../_static/pydoctheme.css" type="text/css" />
    <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />

    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    '../',
        VERSION:     '3.5.2',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="../_static/jquery.js"></script>
    <script type="text/javascript" src="../_static/underscore.js"></script>
    <script type="text/javascript" src="../_static/doctools.js"></script>
    <script type="text/javascript" src="../_static/sidebar.js"></script>
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Python 3.5.2 documentation"
          href="../_static/opensearch.xml"/>
    <link rel="author" title="About these documents" href="../about.html" />
    <link rel="copyright" title="Copyright" href="../copyright.html" />
    <link rel="top" title="Python 3.5.2 documentation" href="../contents.html" />
    <link rel="up" title="25. Graphical User Interfaces with Tk" href="tk.html" />
    <link rel="next" title="25.6. Other Graphical User Interface Packages" href="othergui.html" />
    <link rel="prev" title="25.4. tkinter.scrolledtext — Scrolled Text Widget" href="tkinter.scrolledtext.html" />
    <link rel="shortcut icon" type="image/png" href="../_static/py.png" />
    <script type="text/javascript" src="../_static/copybutton.js"></script>




  </head>
  <body role="document">
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="othergui.html" title="25.6. Other Graphical User Interface Packages"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="tkinter.scrolledtext.html" title="25.4. tkinter.scrolledtext — Scrolled Text Widget"
             accesskey="P">previous</a> |</li>
        <li><img src="../_static/py.png" alt=""
                 style="vertical-align: middle; margin-top: -1px"/></li>
        <li><a href="https://www.python.org/">Python</a> &raquo;</li>
        <li>
          <a href="../index.html">3.5.2 Documentation</a> &raquo;
        </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &raquo;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" accesskey="U">25. Graphical User Interfaces with Tk</a> &raquo;</li>
    <li class="right">


    <div class="inline-search" style="display: none" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" type="text" name="q" />
          <input type="submit" value="Go" />
          <input type="hidden" name="check_keywords" value="yes" />
          <input type="hidden" name="area" value="default" />
        </form>
    </div>
    <script type="text/javascript">$('.inline-search').show(0);</script>
         |
    </li>

      </ul>
    </div>

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">

  <div class="section" id="idle">
<span id="id1"></span><h1>25.5. IDLE<a class="headerlink" href="#idle" title="Permalink to this headline">¶</a></h1>
<p><strong>Source code:</strong> <a class="reference external" href="https://github.com/python/cpython/tree/3.5/Lib/idlelib">Lib/idlelib/</a></p>
<hr class="docutils" id="index-0" />
<p>IDLE is Python&#8217;s Integrated Development and Learning Environment.</p>
<p>IDLE has the following features:</p>
<ul class="simple">
<li>coded in 100% pure Python, using the <a class="reference internal" href="tkinter.html#module-tkinter" title="tkinter: Interface to Tcl/Tk for graphical user interfaces"><code class="xref py py-mod docutils literal"><span class="pre">tkinter</span></code></a> GUI toolkit</li>
<li>cross-platform: works mostly the same on Windows, Unix, and Mac OS X</li>
<li>Python shell window (interactive interpreter) with colorizing
of code input, output, and error messages</li>
<li>multi-window text editor with multiple undo, Python colorizing,
smart indent, call tips, auto completion, and other features</li>
<li>search within any window, replace within editor windows, and search
through multiple files (grep)</li>
<li>debugger with persistent breakpoints, stepping, and viewing
of global and local namespaces</li>
<li>configuration, browsers, and other dialogs</li>
</ul>
<div class="section" id="menus">
<h2>25.5.1. Menus<a class="headerlink" href="#menus" title="Permalink to this headline">¶</a></h2>
<p>IDLE has two main window types, the Shell window and the Editor window.  It is
possible to have multiple editor windows simultaneously.  Output windows, such
as used for Edit / Find in Files, are a subtype of edit window.  They currently
have the same top menu as Editor windows but a different default title and
context menu.</p>
<p>IDLE&#8217;s menus dynamically change based on which window is currently selected.
Each menu documented below indicates which window type it is associated with.</p>
<div class="section" id="file-menu-shell-and-editor">
<h3>********. File menu (Shell and Editor)<a class="headerlink" href="#file-menu-shell-and-editor" title="Permalink to this headline">¶</a></h3>
<dl class="docutils">
<dt>New File</dt>
<dd>Create a new file editing window.</dd>
<dt>Open...</dt>
<dd>Open an existing file with an Open dialog.</dd>
<dt>Recent Files</dt>
<dd>Open a list of recent files.  Click one to open it.</dd>
<dt>Open Module...</dt>
<dd>Open an existing module (searches sys.path).</dd>
</dl>
<dl class="docutils" id="index-1">
<dt>Class Browser</dt>
<dd>Show functions, classes, and methods in the current Editor file in a
tree structure.  In the shell, open a module first.</dd>
<dt>Path Browser</dt>
<dd>Show sys.path directories, modules, functions, classes and methods in a
tree structure.</dd>
<dt>Save</dt>
<dd>Save the current window to the associated file, if there is one.  Windows
that have been changed since being opened or last saved have a * before
and after the window title.  If there is no associated file,
do Save As instead.</dd>
<dt>Save As...</dt>
<dd>Save the current window with a Save As dialog.  The file saved becomes the
new associated file for the window.</dd>
<dt>Save Copy As...</dt>
<dd>Save the current window to different file without changing the associated
file.</dd>
<dt>Print Window</dt>
<dd>Print the current window to the default printer.</dd>
<dt>Close</dt>
<dd>Close the current window (ask to save if unsaved).</dd>
<dt>Exit</dt>
<dd>Close all windows and quit IDLE (ask to save unsaved windows).</dd>
</dl>
</div>
<div class="section" id="edit-menu-shell-and-editor">
<h3>********. Edit menu (Shell and Editor)<a class="headerlink" href="#edit-menu-shell-and-editor" title="Permalink to this headline">¶</a></h3>
<dl class="docutils">
<dt>Undo</dt>
<dd>Undo the last change to the current window.  A maximum of 1000 changes may
be undone.</dd>
<dt>Redo</dt>
<dd>Redo the last undone change to the current window.</dd>
<dt>Cut</dt>
<dd>Copy selection into the system-wide clipboard; then delete the selection.</dd>
<dt>Copy</dt>
<dd>Copy selection into the system-wide clipboard.</dd>
<dt>Paste</dt>
<dd>Insert contents of the system-wide clipboard into the current window.</dd>
</dl>
<p>The clipboard functions are also available in context menus.</p>
<dl class="docutils">
<dt>Select All</dt>
<dd>Select the entire contents of the current window.</dd>
<dt>Find...</dt>
<dd>Open a search dialog with many options</dd>
<dt>Find Again</dt>
<dd>Repeat the last search, if there is one.</dd>
<dt>Find Selection</dt>
<dd>Search for the currently selected string, if there is one.</dd>
<dt>Find in Files...</dt>
<dd>Open a file search dialog.  Put results in a new output window.</dd>
<dt>Replace...</dt>
<dd>Open a search-and-replace dialog.</dd>
<dt>Go to Line</dt>
<dd>Move cursor to the line number requested and make that line visible.</dd>
<dt>Show Completions</dt>
<dd>Open a scrollable list allowing selection of keywords and attributes. See
Completions in the Tips sections below.</dd>
<dt>Expand Word</dt>
<dd>Expand a prefix you have typed to match a full word in the same window;
repeat to get a different expansion.</dd>
<dt>Show call tip</dt>
<dd>After an unclosed parenthesis for a function, open a small window with
function parameter hints.</dd>
<dt>Show surrounding parens</dt>
<dd>Highlight the surrounding parenthesis.</dd>
</dl>
</div>
<div class="section" id="format-menu-editor-window-only">
<h3>********. Format menu (Editor window only)<a class="headerlink" href="#format-menu-editor-window-only" title="Permalink to this headline">¶</a></h3>
<dl class="docutils">
<dt>Indent Region</dt>
<dd>Shift selected lines right by the indent width (default 4 spaces).</dd>
<dt>Dedent Region</dt>
<dd>Shift selected lines left by the indent width (default 4 spaces).</dd>
<dt>Comment Out Region</dt>
<dd>Insert ## in front of selected lines.</dd>
<dt>Uncomment Region</dt>
<dd>Remove leading # or ## from selected lines.</dd>
<dt>Tabify Region</dt>
<dd>Turn <em>leading</em> stretches of spaces into tabs. (Note: We recommend using
4 space blocks to indent Python code.)</dd>
<dt>Untabify Region</dt>
<dd>Turn <em>all</em> tabs into the correct number of spaces.</dd>
<dt>Toggle Tabs</dt>
<dd>Open a dialog to switch between indenting with spaces and tabs.</dd>
<dt>New Indent Width</dt>
<dd>Open a dialog to change indent width. The accepted default by the Python
community is 4 spaces.</dd>
<dt>Format Paragraph</dt>
<dd>Reformat the current blank-line-delimited paragraph in comment block or
multiline string or selected line in a string.  All lines in the
paragraph will be formatted to less than N columns, where N defaults to 72.</dd>
<dt>Strip trailing whitespace</dt>
<dd>Remove any space characters after the last non-space character of a line.</dd>
</dl>
</div>
<div class="section" id="run-menu-editor-window-only">
<span id="index-2"></span><h3>********. Run menu (Editor window only)<a class="headerlink" href="#run-menu-editor-window-only" title="Permalink to this headline">¶</a></h3>
<dl class="docutils">
<dt>Python Shell</dt>
<dd>Open or wake up the Python Shell window.</dd>
<dt>Check Module</dt>
<dd>Check the syntax of the module currently open in the Editor window. If the
module has not been saved IDLE will either prompt the user to save or
autosave, as selected in the General tab of the Idle Settings dialog.  If
there is a syntax error, the approximate location is indicated in the
Editor window.</dd>
<dt>Run Module</dt>
<dd>Do Check Module (above).  If no error, restart the shell to clean the
environment, then execute the module.  Output is displayed in the Shell
window.  Note that output requires use of <code class="docutils literal"><span class="pre">print</span></code> or <code class="docutils literal"><span class="pre">write</span></code>.
When execution is complete, the Shell retains focus and displays a prompt.
At this point, one may interactively explore the result of execution.
This is similar to executing a file with <code class="docutils literal"><span class="pre">python</span> <span class="pre">-i</span> <span class="pre">file</span></code> at a command
line.</dd>
</dl>
</div>
<div class="section" id="shell-menu-shell-window-only">
<h3>********. Shell menu (Shell window only)<a class="headerlink" href="#shell-menu-shell-window-only" title="Permalink to this headline">¶</a></h3>
<dl class="docutils">
<dt>View Last Restart</dt>
<dd>Scroll the shell window to the last Shell restart.</dd>
<dt>Restart Shell</dt>
<dd>Restart the shell to clean the environment.</dd>
<dt>Interrupt Execution</dt>
<dd>Stop a running program.</dd>
</dl>
</div>
<div class="section" id="debug-menu-shell-window-only">
<h3>25.5.1.6. Debug menu (Shell window only)<a class="headerlink" href="#debug-menu-shell-window-only" title="Permalink to this headline">¶</a></h3>
<dl class="docutils">
<dt>Go to File/Line</dt>
<dd>Look on the current line. with the cursor, and the line above for a filename
and line number.  If found, open the file if not already open, and show the
line.  Use this to view source lines referenced in an exception traceback
and lines found by Find in Files. Also available in the context menu of
the Shell window and Output windows.</dd>
</dl>
<dl class="docutils" id="index-3">
<dt>Debugger (toggle)</dt>
<dd>When actived, code entered in the Shell or run from an Editor will run
under the debugger.  In the Editor, breakpoints can be set with the context
menu.  This feature is still incomplete and somewhat experimental.</dd>
<dt>Stack Viewer</dt>
<dd>Show the stack traceback of the last exception in a tree widget, with
access to locals and globals.</dd>
<dt>Auto-open Stack Viewer</dt>
<dd>Toggle automatically opening the stack viewer on an unhandled exception.</dd>
</dl>
</div>
<div class="section" id="options-menu-shell-and-editor">
<h3>25.5.1.7. Options menu (Shell and Editor)<a class="headerlink" href="#options-menu-shell-and-editor" title="Permalink to this headline">¶</a></h3>
<dl class="docutils">
<dt>Configure IDLE</dt>
<dd><p class="first">Open a configuration dialog and change preferences for the following:
fonts, indentation, keybindings, text color themes, startup windows and
size, additional help sources, and extensions (see below).  On OS X,
open the configuration dialog by selecting Preferences in the application
menu.  To use a new built-in color theme (IDLE Dark) with older IDLEs,
save it as a new custom theme.</p>
<p class="last">Non-default user settings are saved in a .idlerc directory in the user&#8217;s
home directory.  Problems caused by bad user configuration files are solved
by editing or deleting one or more of the files in .idlerc.</p>
</dd>
<dt>Code Context (toggle)(Editor Window only)</dt>
<dd>Open a pane at the top of the edit window which shows the block context
of the code which has scrolled above the top of the window.</dd>
</dl>
</div>
<div class="section" id="window-menu-shell-and-editor">
<h3>25.5.1.8. Window menu (Shell and Editor)<a class="headerlink" href="#window-menu-shell-and-editor" title="Permalink to this headline">¶</a></h3>
<dl class="docutils">
<dt>Zoom Height</dt>
<dd>Toggles the window between normal size and maximum height. The initial size
defaults to 40 lines by 80 chars unless changed on the General tab of the
Configure IDLE dialog.</dd>
</dl>
<p>The rest of this menu lists the names of all open windows; select one to bring
it to the foreground (deiconifying it if necessary).</p>
</div>
<div class="section" id="help-menu-shell-and-editor">
<h3>********. Help menu (Shell and Editor)<a class="headerlink" href="#help-menu-shell-and-editor" title="Permalink to this headline">¶</a></h3>
<dl class="docutils">
<dt>About IDLE</dt>
<dd>Display version, copyright, license, credits, and more.</dd>
<dt>IDLE Help</dt>
<dd>Display a help file for IDLE detailing the menu options, basic editing and
navigation, and other tips.</dd>
<dt>Python Docs</dt>
<dd>Access local Python documentation, if installed, or start a web browser
and open docs.python.org showing the latest Python documentation.</dd>
<dt>Turtle Demo</dt>
<dd>Run the turtledemo module with example python code and turtle drawings.</dd>
</dl>
<p>Additional help sources may be added here with the Configure IDLE dialog under
the General tab.</p>
</div>
<div class="section" id="context-menus">
<span id="index-4"></span><h3>********0. Context Menus<a class="headerlink" href="#context-menus" title="Permalink to this headline">¶</a></h3>
<p>Open a context menu by right-clicking in a window (Control-click on OS X).
Context menus have the standard clipboard functions also on the Edit menu.</p>
<dl class="docutils">
<dt>Cut</dt>
<dd>Copy selection into the system-wide clipboard; then delete the selection.</dd>
<dt>Copy</dt>
<dd>Copy selection into the system-wide clipboard.</dd>
<dt>Paste</dt>
<dd>Insert contents of the system-wide clipboard into the current window.</dd>
</dl>
<p>Editor windows also have breakpoint functions.  Lines with a breakpoint set are
specially marked.  Breakpoints only have an effect when running under the
debugger.  Breakpoints for a file are saved in the user&#8217;s .idlerc directory.</p>
<dl class="docutils">
<dt>Set Breakpoint</dt>
<dd>Set a breakpoint on the current line.</dd>
<dt>Clear Breakpoint</dt>
<dd>Clear the breakpoint on that line.</dd>
</dl>
<p>Shell and Output windows have the following.</p>
<dl class="docutils">
<dt>Go to file/line</dt>
<dd>Same as in Debug menu.</dd>
</dl>
</div>
</div>
<div class="section" id="editing-and-navigation">
<h2>25.5.2. Editing and navigation<a class="headerlink" href="#editing-and-navigation" title="Permalink to this headline">¶</a></h2>
<p>In this section, &#8216;C&#8217; refers to the <code class="kbd docutils literal"><span class="pre">Control</span></code> key on Windows and Unix and
the <code class="kbd docutils literal"><span class="pre">Command</span></code> key on Mac OSX.</p>
<ul>
<li><p class="first"><code class="kbd docutils literal"><span class="pre">Backspace</span></code> deletes to the left; <code class="kbd docutils literal"><span class="pre">Del</span></code> deletes to the right</p>
</li>
<li><p class="first"><code class="kbd docutils literal"><span class="pre">C-Backspace</span></code> delete word left; <code class="kbd docutils literal"><span class="pre">C-Del</span></code> delete word to the right</p>
</li>
<li><p class="first">Arrow keys and <code class="kbd docutils literal"><span class="pre">Page</span> <span class="pre">Up</span></code>/<code class="kbd docutils literal"><span class="pre">Page</span> <span class="pre">Down</span></code> to move around</p>
</li>
<li><p class="first"><code class="kbd docutils literal"><span class="pre">C-LeftArrow</span></code> and <code class="kbd docutils literal"><span class="pre">C-RightArrow</span></code> moves by words</p>
</li>
<li><p class="first"><code class="kbd docutils literal"><span class="pre">Home</span></code>/<code class="kbd docutils literal"><span class="pre">End</span></code> go to begin/end of line</p>
</li>
<li><p class="first"><code class="kbd docutils literal"><span class="pre">C-Home</span></code>/<code class="kbd docutils literal"><span class="pre">C-End</span></code> go to begin/end of file</p>
</li>
<li><p class="first">Some useful Emacs bindings are inherited from Tcl/Tk:</p>
<blockquote>
<div><ul class="simple">
<li><code class="kbd docutils literal"><span class="pre">C-a</span></code> beginning of line</li>
<li><code class="kbd docutils literal"><span class="pre">C-e</span></code> end of line</li>
<li><code class="kbd docutils literal"><span class="pre">C-k</span></code> kill line (but doesn&#8217;t put it in clipboard)</li>
<li><code class="kbd docutils literal"><span class="pre">C-l</span></code> center window around the insertion point</li>
<li><code class="kbd docutils literal"><span class="pre">C-b</span></code> go backwards one character without deleting (usually you can
also use the cursor key for this)</li>
<li><code class="kbd docutils literal"><span class="pre">C-f</span></code> go forward one character without deleting (usually you can
also use the cursor key for this)</li>
<li><code class="kbd docutils literal"><span class="pre">C-p</span></code> go up one line (usually you can also use the cursor key for
this)</li>
<li><code class="kbd docutils literal"><span class="pre">C-d</span></code> delete next character</li>
</ul>
</div></blockquote>
</li>
</ul>
<p>Standard keybindings (like <code class="kbd docutils literal"><span class="pre">C-c</span></code> to copy and <code class="kbd docutils literal"><span class="pre">C-v</span></code> to paste)
may work.  Keybindings are selected in the Configure IDLE dialog.</p>
<div class="section" id="automatic-indentation">
<h3>********. Automatic indentation<a class="headerlink" href="#automatic-indentation" title="Permalink to this headline">¶</a></h3>
<p>After a block-opening statement, the next line is indented by 4 spaces (in the
Python Shell window by one tab).  After certain keywords (break, return etc.)
the next line is dedented.  In leading indentation, <code class="kbd docutils literal"><span class="pre">Backspace</span></code> deletes up
to 4 spaces if they are there. <code class="kbd docutils literal"><span class="pre">Tab</span></code> inserts spaces (in the Python
Shell window one tab), number depends on Indent width. Currently tabs
are restricted to four spaces due to Tcl/Tk limitations.</p>
<p>See also the indent/dedent region commands in the edit menu.</p>
</div>
<div class="section" id="completions">
<h3>********. Completions<a class="headerlink" href="#completions" title="Permalink to this headline">¶</a></h3>
<p>Completions are supplied for functions, classes, and attributes of classes,
both built-in and user-defined. Completions are also provided for
filenames.</p>
<p>The AutoCompleteWindow (ACW) will open after a predefined delay (default is
two seconds) after a &#8216;.&#8217; or (in a string) an os.sep is typed. If after one
of those characters (plus zero or more other characters) a tab is typed
the ACW will open immediately if a possible continuation is found.</p>
<p>If there is only one possible completion for the characters entered, a
<code class="kbd docutils literal"><span class="pre">Tab</span></code> will supply that completion without opening the ACW.</p>
<p>&#8216;Show Completions&#8217; will force open a completions window, by default the
<code class="kbd docutils literal"><span class="pre">C-space</span></code> will open a completions window. In an empty
string, this will contain the files in the current directory. On a
blank line, it will contain the built-in and user-defined functions and
classes in the current name spaces, plus any modules imported. If some
characters have been entered, the ACW will attempt to be more specific.</p>
<p>If a string of characters is typed, the ACW selection will jump to the
entry most closely matching those characters.  Entering a <code class="kbd docutils literal"><span class="pre">tab</span></code> will
cause the longest non-ambiguous match to be entered in the Editor window or
Shell.  Two <code class="kbd docutils literal"><span class="pre">tab</span></code> in a row will supply the current ACW selection, as
will return or a double click.  Cursor keys, Page Up/Down, mouse selection,
and the scroll wheel all operate on the ACW.</p>
<p>&#8220;Hidden&#8221; attributes can be accessed by typing the beginning of hidden
name after a &#8216;.&#8217;, e.g. &#8216;_&#8217;. This allows access to modules with
<code class="docutils literal"><span class="pre">__all__</span></code> set, or to class-private attributes.</p>
<p>Completions and the &#8216;Expand Word&#8217; facility can save a lot of typing!</p>
<p>Completions are currently limited to those in the namespaces. Names in
an Editor window which are not via <code class="docutils literal"><span class="pre">__main__</span></code> and <a class="reference internal" href="sys.html#sys.modules" title="sys.modules"><code class="xref py py-data docutils literal"><span class="pre">sys.modules</span></code></a> will
not be found.  Run the module once with your imports to correct this situation.
Note that IDLE itself places quite a few modules in sys.modules, so
much can be found by default, e.g. the re module.</p>
<p>If you don&#8217;t like the ACW popping up unbidden, simply make the delay
longer or disable the extension.</p>
</div>
<div class="section" id="calltips">
<h3>********. Calltips<a class="headerlink" href="#calltips" title="Permalink to this headline">¶</a></h3>
<p>A calltip is shown when one types <code class="kbd docutils literal"><span class="pre">(</span></code> after the name of an <em>acccessible</em>
function.  A name expression may include dots and subscripts.  A calltip
remains until it is clicked, the cursor is moved out of the argument area,
or <code class="kbd docutils literal"><span class="pre">)</span></code> is typed.  When the cursor is in the argument part of a definition,
the menu or shortcut display a calltip.</p>
<p>A calltip consists of the function signature and the first line of the
docstring.  For builtins without an accessible signature, the calltip
consists of all lines up the fifth line or the first blank line.  These
details may change.</p>
<p>The set of <em>accessible</em> functions depends on what modules have been imported
into the user process, including those imported by Idle itself,
and what definitions have been run, all since the last restart.</p>
<p>For example, restart the Shell and enter <code class="docutils literal"><span class="pre">itertools.count(</span></code>.  A calltip
appears because Idle imports itertools into the user process for its own use.
(This could change.)  Enter <code class="docutils literal"><span class="pre">turtle.write(</span></code> and nothing appears.  Idle does
not import turtle.  The menu or shortcut do nothing either.  Enter
<code class="docutils literal"><span class="pre">import</span> <span class="pre">turtle</span></code> and then <code class="docutils literal"><span class="pre">turtle.write(</span></code> will work.</p>
<p>In an editor, import statements have no effect until one runs the file.  One
might want to run a file after writing the import statements at the top,
or immediately run an existing file before editing.</p>
</div>
<div class="section" id="python-shell-window">
<h3>********. Python Shell window<a class="headerlink" href="#python-shell-window" title="Permalink to this headline">¶</a></h3>
<ul>
<li><p class="first"><code class="kbd docutils literal"><span class="pre">C-c</span></code> interrupts executing command</p>
</li>
<li><p class="first"><code class="kbd docutils literal"><span class="pre">C-d</span></code> sends end-of-file; closes window if typed at a <code class="docutils literal"><span class="pre">&gt;&gt;&gt;</span></code> prompt</p>
</li>
<li><p class="first"><code class="kbd docutils literal"><span class="pre">Alt-/</span></code> (Expand word) is also useful to reduce typing</p>
<p>Command history</p>
<ul class="simple">
<li><code class="kbd docutils literal"><span class="pre">Alt-p</span></code> retrieves previous command matching what you have typed. On
OS X use <code class="kbd docutils literal"><span class="pre">C-p</span></code>.</li>
<li><code class="kbd docutils literal"><span class="pre">Alt-n</span></code> retrieves next. On OS X use <code class="kbd docutils literal"><span class="pre">C-n</span></code>.</li>
<li><code class="kbd docutils literal"><span class="pre">Return</span></code> while on any previous command retrieves that command</li>
</ul>
</li>
</ul>
</div>
<div class="section" id="text-colors">
<h3>********. Text colors<a class="headerlink" href="#text-colors" title="Permalink to this headline">¶</a></h3>
<p>Idle defaults to black on white text, but colors text with special meanings.
For the shell, these are shell output, shell error, user output, and
user error.  For Python code, at the shell prompt or in an editor, these are
keywords, builtin class and function names, names following <code class="docutils literal"><span class="pre">class</span></code> and
<code class="docutils literal"><span class="pre">def</span></code>, strings, and comments. For any text window, these are the cursor (when
present), found text (when possible), and selected text.</p>
<p>Text coloring is done in the background, so uncolorized text is occasionally
visible.  To change the color scheme, use the Configure IDLE dialog
Highlighting tab.  The marking of debugger breakpoint lines in the editor and
text in popups and dialogs is not user-configurable.</p>
</div>
</div>
<div class="section" id="startup-and-code-execution">
<h2>25.5.3. Startup and code execution<a class="headerlink" href="#startup-and-code-execution" title="Permalink to this headline">¶</a></h2>
<p>Upon startup with the <code class="docutils literal"><span class="pre">-s</span></code> option, IDLE will execute the file referenced by
the environment variables <span class="target" id="index-5"></span><code class="xref std std-envvar docutils literal"><span class="pre">IDLESTARTUP</span></code> or <span class="target" id="index-6"></span><a class="reference internal" href="../using/cmdline.html#envvar-PYTHONSTARTUP"><code class="xref std std-envvar docutils literal"><span class="pre">PYTHONSTARTUP</span></code></a>.
IDLE first checks for <code class="docutils literal"><span class="pre">IDLESTARTUP</span></code>; if <code class="docutils literal"><span class="pre">IDLESTARTUP</span></code> is present the file
referenced is run.  If <code class="docutils literal"><span class="pre">IDLESTARTUP</span></code> is not present, IDLE checks for
<code class="docutils literal"><span class="pre">PYTHONSTARTUP</span></code>.  Files referenced by these environment variables are
convenient places to store functions that are used frequently from the IDLE
shell, or for executing import statements to import common modules.</p>
<p>In addition, <code class="docutils literal"><span class="pre">Tk</span></code> also loads a startup file if it is present.  Note that the
Tk file is loaded unconditionally.  This additional file is <code class="docutils literal"><span class="pre">.Idle.py</span></code> and is
looked for in the user&#8217;s home directory.  Statements in this file will be
executed in the Tk namespace, so this file is not useful for importing
functions to be used from IDLE&#8217;s Python shell.</p>
<div class="section" id="command-line-usage">
<h3>********. Command line usage<a class="headerlink" href="#command-line-usage" title="Permalink to this headline">¶</a></h3>
<div class="highlight-none"><div class="highlight"><pre><span></span>idle.py [-c command] [-d] [-e] [-h] [-i] [-r file] [-s] [-t title] [-] [arg] ...

-c command  run command in the shell window
-d          enable debugger and open shell window
-e          open editor window
-h          print help message with legal combinations and exit
-i          open shell window
-r file     run file in shell window
-s          run $IDLESTARTUP or $PYTHONSTARTUP first, in shell window
-t title    set title of shell window
-           run stdin in shell (- must be last option before args)
</pre></div>
</div>
<p>If there are arguments:</p>
<ul class="simple">
<li>If <code class="docutils literal"><span class="pre">-</span></code>, <code class="docutils literal"><span class="pre">-c</span></code>, or <code class="docutils literal"><span class="pre">r</span></code> is used, all arguments are placed in
<code class="docutils literal"><span class="pre">sys.argv[1:...]</span></code> and <code class="docutils literal"><span class="pre">sys.argv[0]</span></code> is set to <code class="docutils literal"><span class="pre">''</span></code>, <code class="docutils literal"><span class="pre">'-c'</span></code>,
or <code class="docutils literal"><span class="pre">'-r'</span></code>.  No editor window is opened, even if that is the default
set in the Options dialog.</li>
<li>Otherwise, arguments are files opened for editing and
<code class="docutils literal"><span class="pre">sys.argv</span></code> reflects the arguments passed to IDLE itself.</li>
</ul>
</div>
<div class="section" id="idle-console-differences">
<h3>********. IDLE-console differences<a class="headerlink" href="#idle-console-differences" title="Permalink to this headline">¶</a></h3>
<p>As much as possible, the result of executing Python code with IDLE is the
same as executing the same code in a console window.  However, the different
interface and operation occasionally affects visible results.  For instance,
<code class="docutils literal"><span class="pre">sys.modules</span></code> starts with more entries.</p>
<p>IDLE also replaces <code class="docutils literal"><span class="pre">sys.stdin</span></code>, <code class="docutils literal"><span class="pre">sys.stdout</span></code>, and <code class="docutils literal"><span class="pre">sys.stderr</span></code> with
objects that get input from and send output to the Shell window.
When this window has the focus, it controls the keyboard and screen.
This is normally transparent, but functions that directly access the keyboard
and screen will not work.  If <code class="docutils literal"><span class="pre">sys</span></code> is reset with <code class="docutils literal"><span class="pre">importlib.reload(sys)</span></code>,
IDLE&#8217;s changes are lost and things like <code class="docutils literal"><span class="pre">input</span></code>, <code class="docutils literal"><span class="pre">raw_input</span></code>, and
<code class="docutils literal"><span class="pre">print</span></code> will not work correctly.</p>
<p>With IDLE&#8217;s Shell, one enters, edits, and recalls complete statements.
Some consoles only work with a single physical line at a time.  IDLE uses
<code class="docutils literal"><span class="pre">exec</span></code> to run each statement.  As a result, <code class="docutils literal"><span class="pre">'__builtins__'</span></code> is always
defined for each statement.</p>
</div>
<div class="section" id="running-without-a-subprocess">
<h3>********. Running without a subprocess<a class="headerlink" href="#running-without-a-subprocess" title="Permalink to this headline">¶</a></h3>
<p>By default, IDLE executes user code in a separate subprocess via a socket,
which uses the internal loopback interface.  This connection is not
externally visible and no data is sent to or received from the Internet.
If firewall software complains anyway, you can ignore it.</p>
<p>If the attempt to make the socket connection fails, Idle will notify you.
Such failures are sometimes transient, but if persistent, the problem
may be either a firewall blocking the connecton or misconfiguration of
a particular system.  Until the problem is fixed, one can run Idle with
the -n command line switch.</p>
<p>If IDLE is started with the -n command line switch it will run in a
single process and will not create the subprocess which runs the RPC
Python execution server.  This can be useful if Python cannot create
the subprocess or the RPC socket interface on your platform.  However,
in this mode user code is not isolated from IDLE itself.  Also, the
environment is not restarted when Run/Run Module (F5) is selected.  If
your code has been modified, you must reload() the affected modules and
re-import any specific items (e.g. from foo import baz) if the changes
are to take effect.  For these reasons, it is preferable to run IDLE
with the default subprocess if at all possible.</p>
<div class="deprecated">
<p><span class="versionmodified">Deprecated since version 3.4.</span></p>
</div>
</div>
</div>
<div class="section" id="help-and-preferences">
<h2>25.5.4. Help and preferences<a class="headerlink" href="#help-and-preferences" title="Permalink to this headline">¶</a></h2>
<div class="section" id="additional-help-sources">
<h3>********. Additional help sources<a class="headerlink" href="#additional-help-sources" title="Permalink to this headline">¶</a></h3>
<p>IDLE includes a help menu entry called &#8220;Python Docs&#8221; that will open the
extensive sources of help, including tutorials, available at docs.python.org.
Selected URLs can be added or removed from the help menu at any time using the
Configure IDLE dialog. See the IDLE help option in the help menu of IDLE for
more information.</p>
</div>
<div class="section" id="setting-preferences">
<h3>********. Setting preferences<a class="headerlink" href="#setting-preferences" title="Permalink to this headline">¶</a></h3>
<p>The font preferences, highlighting, keys, and general preferences can be
changed via Configure IDLE on the Option menu.  Keys can be user defined;
IDLE ships with four built in key sets. In addition a user can create a
custom key set in the Configure IDLE dialog under the keys tab.</p>
</div>
<div class="section" id="extensions">
<h3>********. Extensions<a class="headerlink" href="#extensions" title="Permalink to this headline">¶</a></h3>
<p>IDLE contains an extension facility.  Peferences for extensions can be
changed with Configure Extensions. See the beginning of config-extensions.def
in the idlelib directory for further information.  The default extensions
are currently:</p>
<ul class="simple">
<li>FormatParagraph</li>
<li>AutoExpand</li>
<li>ZoomHeight</li>
<li>ScriptBinding</li>
<li>CallTips</li>
<li>ParenMatch</li>
<li>AutoComplete</li>
<li>CodeContext</li>
<li>RstripExtension</li>
</ul>
</div>
</div>
</div>


          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="../contents.html">Table Of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">25.5. IDLE</a><ul>
<li><a class="reference internal" href="#menus">25.5.1. Menus</a><ul>
<li><a class="reference internal" href="#file-menu-shell-and-editor">********. File menu (Shell and Editor)</a></li>
<li><a class="reference internal" href="#edit-menu-shell-and-editor">********. Edit menu (Shell and Editor)</a></li>
<li><a class="reference internal" href="#format-menu-editor-window-only">********. Format menu (Editor window only)</a></li>
<li><a class="reference internal" href="#run-menu-editor-window-only">********. Run menu (Editor window only)</a></li>
<li><a class="reference internal" href="#shell-menu-shell-window-only">********. Shell menu (Shell window only)</a></li>
<li><a class="reference internal" href="#debug-menu-shell-window-only">25.5.1.6. Debug menu (Shell window only)</a></li>
<li><a class="reference internal" href="#options-menu-shell-and-editor">25.5.1.7. Options menu (Shell and Editor)</a></li>
<li><a class="reference internal" href="#window-menu-shell-and-editor">25.5.1.8. Window menu (Shell and Editor)</a></li>
<li><a class="reference internal" href="#help-menu-shell-and-editor">********. Help menu (Shell and Editor)</a></li>
<li><a class="reference internal" href="#context-menus">********0. Context Menus</a></li>
</ul>
</li>
<li><a class="reference internal" href="#editing-and-navigation">25.5.2. Editing and navigation</a><ul>
<li><a class="reference internal" href="#automatic-indentation">********. Automatic indentation</a></li>
<li><a class="reference internal" href="#completions">********. Completions</a></li>
<li><a class="reference internal" href="#calltips">********. Calltips</a></li>
<li><a class="reference internal" href="#python-shell-window">********. Python Shell window</a></li>
<li><a class="reference internal" href="#text-colors">********. Text colors</a></li>
</ul>
</li>
<li><a class="reference internal" href="#startup-and-code-execution">25.5.3. Startup and code execution</a><ul>
<li><a class="reference internal" href="#command-line-usage">********. Command line usage</a></li>
<li><a class="reference internal" href="#idle-console-differences">********. IDLE-console differences</a></li>
<li><a class="reference internal" href="#running-without-a-subprocess">********. Running without a subprocess</a></li>
</ul>
</li>
<li><a class="reference internal" href="#help-and-preferences">25.5.4. Help and preferences</a><ul>
<li><a class="reference internal" href="#additional-help-sources">********. Additional help sources</a></li>
<li><a class="reference internal" href="#setting-preferences">********. Setting preferences</a></li>
<li><a class="reference internal" href="#extensions">********. Extensions</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="tkinter.scrolledtext.html"
                        title="previous chapter">25.4. <code class="docutils literal"><span class="pre">tkinter.scrolledtext</span></code> &#8212; Scrolled Text Widget</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="othergui.html"
                        title="next chapter">25.6. Other Graphical User Interface Packages</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../bugs.html">Report a Bug</a></li>
      <li><a href="../_sources/library/idle.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
  </div>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="othergui.html" title="25.6. Other Graphical User Interface Packages"
             >next</a> |</li>
        <li class="right" >
          <a href="tkinter.scrolledtext.html" title="25.4. tkinter.scrolledtext — Scrolled Text Widget"
             >previous</a> |</li>
        <li><img src="../_static/py.png" alt=""
                 style="vertical-align: middle; margin-top: -1px"/></li>
        <li><a href="https://www.python.org/">Python</a> &raquo;</li>
        <li>
          <a href="../index.html">3.5.2 Documentation</a> &raquo;
        </li>

          <li class="nav-item nav-item-1"><a href="index.html" >The Python Standard Library</a> &raquo;</li>
          <li class="nav-item nav-item-2"><a href="tk.html" >25. Graphical User Interfaces with Tk</a> &raquo;</li>
    <li class="right">


    <div class="inline-search" style="display: none" role="search">
        <form class="inline-search" action="../search.html" method="get">
          <input placeholder="Quick search" type="text" name="q" />
          <input type="submit" value="Go" />
          <input type="hidden" name="check_keywords" value="yes" />
          <input type="hidden" name="area" value="default" />
        </form>
    </div>
    <script type="text/javascript">$('.inline-search').show(0);</script>
         |
    </li>

      </ul>
    </div>
    <div class="footer">
    &copy; <a href="../copyright.html">Copyright</a> 2001-2016, Python Software Foundation.
    <br />
    The Python Software Foundation is a non-profit corporation.
    <a href="https://www.python.org/psf/donations/">Please donate.</a>
    <br />
    Last updated on Sep 12, 2016.
    <a href="../bugs.html">Found a bug</a>?
    <br />
    Created using <a href="http://sphinx.pocoo.org/">Sphinx</a> 1.3.6.
    </div>

  </body>
</html>
