#!/data1/yuancan/compile-tools/python/Python-3.6.1-1.13.1-gdb/bin/python
# EASY-INSTALL-ENTRY-SCRIPT: 'setuptools==40.6.3','console_scripts','easy_install'
__requires__ = 'setuptools==40.6.3'
import re
import sys
from pkg_resources import load_entry_point

if __name__ == '__main__':
    sys.argv[0] = re.sub(r'(-script\.pyw?|\.exe)?$', '', sys.argv[0])
    sys.exit(
        load_entry_point('setuptools==40.6.3', 'console_scripts', 'easy_install')()
    )
