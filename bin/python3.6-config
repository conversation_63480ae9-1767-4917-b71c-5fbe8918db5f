#!/bin/sh

# Keep this script in sync with python-config.in

exit_with_usage ()
{
    echo "Usage: $0 --prefix|--exec-prefix|--includes|--libs|--cflags|--ldflags|--extension-suffix|--help|--abiflags|--configdir"
    exit $1
}

if [ "$1" = "" ] ; then
    exit_with_usage 1
fi

# Returns the actual prefix where this script was installed to.
installed_prefix ()
{
    RESULT=$(dirname $(cd $(dirname "$1") && pwd -P))
    if which readlink >/dev/null 2>&1 ; then
        if readlink -f "$RESULT" >/dev/null 2>&1; then
          RESULT=$(readlink -f "$RESULT")
        fi
    fi
    echo $RESULT
}

prefix_build="/data1/yuancan/compile-tools/python/Python-3.6.1-1.13.1-gdb"
prefix_real=$(installed_prefix "$0")

# Use sed to fix paths from their built-to locations to their installed-to
# locations.
prefix=$(echo "$prefix_build" | sed "s#$prefix_build#$prefix_real#")
exec_prefix_build="${prefix}"
exec_prefix=$(echo "$exec_prefix_build" | sed "s#$exec_prefix_build#$prefix_real#")
includedir=$(echo "${prefix}/include" | sed "s#$prefix_build#$prefix_real#")
libdir=$(echo "${exec_prefix}/lib" | sed "s#$prefix_build#$prefix_real#")
CFLAGS=$(echo "" | sed "s#$prefix_build#$prefix_real#")
VERSION="3.6"
LIBM="-lm"
LIBC=""
SYSLIBS="$LIBM $LIBC"
ABIFLAGS="m"
LIBS="-lpython${VERSION}${ABIFLAGS} -lpthread -ldl  -lutil $SYSLIBS"
BASECFLAGS=" -Wno-unused-result -Wsign-compare"
LDLIBRARY="libpython${VERSION}${ABIFLAGS}.a"
LINKFORSHARED="-Xlinker -export-dynamic"
OPT="-DNDEBUG -g -fwrapv -O3 -Wall -Wstrict-prototypes"
PY_ENABLE_SHARED="0"
LDVERSION="${VERSION}${ABIFLAGS}"
LIBDEST=${prefix}/lib/python${VERSION}
LIBPL=$(echo "${prefix}/lib/python3.6/config-${VERSION}${ABIFLAGS}-x86_64-linux-gnu" | sed "s#$prefix_build#$prefix_real#")
SO=".cpython-36m-x86_64-linux-gnu.so"
PYTHONFRAMEWORK=""
INCDIR="-I$includedir/python${VERSION}${ABIFLAGS}"
PLATINCDIR="-I$includedir/python${VERSION}${ABIFLAGS}"

# Scan for --help or unknown argument.
for ARG in $*
do
    case $ARG in
        --help)
            exit_with_usage 0
        ;;
        --prefix|--exec-prefix|--includes|--libs|--cflags|--ldflags|--extension-suffix|--abiflags|--configdir)
        ;;
        *)
            exit_with_usage 1
        ;;
    esac
done

for ARG in "$@"
do
    case "$ARG" in
        --prefix)
            echo "$prefix"
        ;;
        --exec-prefix)
            echo "$exec_prefix"
        ;;
        --includes)
            echo "$INCDIR $PLATINCDIR"
        ;;
        --cflags)
            echo "$INCDIR $PLATINCDIR $BASECFLAGS $CFLAGS $OPT"
        ;;
        --libs)
            echo "$LIBS"
        ;;
        --ldflags)
            LINKFORSHAREDUSED=
            if [ -z "$PYTHONFRAMEWORK" ] ; then
                LINKFORSHAREDUSED=$LINKFORSHARED
            fi
            LIBPLUSED=
            if [ "$PY_ENABLE_SHARED" = "0" ] ; then
                LIBPLUSED="-L$LIBPL"
            fi
            echo "$LIBPLUSED -L$libdir $LIBS $LINKFORSHAREDUSED"
        ;;
        --extension-suffix)
            echo "$SO"
        ;;
        --abiflags)
            echo "$ABIFLAGS"
        ;;
        --configdir)
            echo "$LIBPL"
        ;;
esac
done
